"""
文件信息转换器
将cluster_info中的file_info转换为search结果格式
"""

import base64
import struct
import json
from scripts.util import log


class FileInfoConverter:
    """将cluster_info中的file_info转换为search结果格式的转换器"""
    
    def __init__(self):
        pass
    
    def parse_qzone_cloud_lloc(self, lloc_code: str) -> tuple:
        """
        解析QZone云存储的lloc编码，提取photo_id和bucket_id
        
        Args:
            lloc_code: file_key中的编码字符串
            
        Returns:
            tuple: (photo_id, bucket_id, error_message)
        """
        try:
            # Base64 解码
            # 反转义
            decoded_lloc = lloc_code.translate(str.maketrans({
                '*': '/',
                '!': '=',
                '.': '+'
            }))
            lloc = base64.urlsafe_b64decode(decoded_lloc + '=' * (-len(lloc_code) % 4))
        except Exception as e:
            return "", "", f"Base64 解码失败: {e}"
        
        # 检查编码类型是否为 '5'
        encode_type_cloud = b'5'
        if len(lloc) < 1:
            return "", "", "LLOC 数据过短，无法读取类型字节"
        if lloc[0:1] != encode_type_cloud:
            return "", "", f"编码类型不匹配，期望 {encode_type_cloud}, 实际为 {lloc[0:1]}"
        
        # 读取 photoID 长度（uint16，小端序）
        if len(lloc) < 3:
            return "", "", "LLOC 数据过短，无法读取 photoID 长度"
        photo_id_len = struct.unpack_from('<H', lloc, 1)[0]  # '<H' 表示小端序 uint16
        
        # 检查 photoID 是否完整
        if len(lloc) < 3 + photo_id_len:
            return "", "", f"photoID 数据不完整，期望 {photo_id_len} 字节，实际 {len(lloc) - 3} 字节"
        
        # 提取 photoID
        photo_id = lloc[3:3 + photo_id_len].decode('utf-8', errors='replace')
        
        # 读取 bucketID 长度（uint16，小端序）
        bucket_id_len_offset = 3 + photo_id_len
        if len(lloc) < bucket_id_len_offset + 2:
            return "", "", "LLOC 数据过短，无法读取 bucketID 长度"
        bucket_id_len = struct.unpack_from('<H', lloc, bucket_id_len_offset)[0]
        
        # 检查 bucketID 是否完整
        bucket_id_offset = bucket_id_len_offset + 2
        if len(lloc) < bucket_id_offset + bucket_id_len:
            return "", "", f"bucketID 数据不完整，期望 {bucket_id_len} 字节，实际 {len(lloc) - bucket_id_offset} 字节"
        
        # 提取 bucketID
        bucket_id = lloc[bucket_id_offset:bucket_id_offset + bucket_id_len].decode('utf-8', errors='replace')
        
        # 验证 bucketID 长度是否一致
        if len(bucket_id) != bucket_id_len:
            return "", "", f"bucketID 长度不一致，期望 {bucket_id_len}，实际 {len(bucket_id)}"
        
        return photo_id, bucket_id, None
    
    def convert_file_info_to_search_result(self, file_info: dict, default_score: str = "S:1.0000[p:0,i:1.0000,t:(raw:1.0000)]") -> list:
        """
        将单个file_info转换为search结果格式
        
        Args:
            file_info: cluster_info中的file_info对象
            default_score: 默认评分字符串
            
        Returns:
            list: [photo_id, [score_string], photo_data] 格式的结果，如果转换失败返回None
        """
        try:
            # 提取基本信息
            file_key = file_info.get('file_key', '')
            tags = file_info.get('tags', {})
            file_type = file_info.get('file_type', 0)
            
            # 解析photo_id
            photo_id, bucket_id, error_message = self.parse_qzone_cloud_lloc(file_key)
            if error_message:
                log(f"解析file_key失败: {error_message}")
                return None
            
            # 构建photo_data，模拟search结果的格式
            photo_data = {
                'photo_id': photo_id,
                'album_desc': '',  # file_info中没有这个字段，设为空
                'album_id': tags.get('Albumid', ''),  # 从tags中获取
                'album_title': '',  # file_info中没有这个字段，设为空
                'ocr': [],  # file_info中没有OCR信息，设为空数组
                'photo_desc': '',  # file_info中没有这个字段，设为空
                'photo_title': '',  # file_info中没有这个字段，设为空
                'shoot_time': '',  # file_info中没有这个字段，设为空
                'upload_time': '',  # file_info中没有这个字段，设为空
                'url': tags.get('url', ''),  # 从tags中获取URL
                'file_type': file_type,
                'file_key': file_key,  # 保留原始file_key
                'bucket_id': bucket_id,  # 添加解析出的bucket_id
            }
            
            # 构建结果格式: [photo_id, [score_string], photo_data]
            result = [photo_id, [default_score], photo_data]
            return result
            
        except Exception as e:
            log(f"转换file_info失败: {str(e)}")
            return None
    
    def convert_file_info_list_to_search_results(self, file_info_list: list, default_score: str = "S:1.0000[p:0,i:1.0000,t:(raw:1.0000)]") -> list:
        """
        将file_info列表转换为search结果格式
        
        Args:
            file_info_list: file_info对象列表
            default_score: 默认评分字符串
            
        Returns:
            list: search结果格式的列表
        """
        results = []
        for file_info in file_info_list:
            result = self.convert_file_info_to_search_result(file_info, default_score)
            if result:
                results.append(result)
        
        log(f"转换完成，输入{len(file_info_list)}个file_info，成功转换{len(results)}个结果")
        return results
    
    def convert_face_list_to_search_results(self, face_list: list, default_score: str = "S:1.0000[p:0,i:1.0000,t:(raw:1.0000)]") -> list:
        """
        将cluster中的face_list转换为search结果格式
        
        Args:
            face_list: cluster中的face_list，每个元素包含file_info
            default_score: 默认评分字符串
            
        Returns:
            list: search结果格式的列表
        """
        file_info_list = []
        for face in face_list:
            file_info = face.get('file_info')
            if file_info:
                file_info_list.append(file_info)
        
        return self.convert_file_info_list_to_search_results(file_info_list, default_score)
    
    def convert_cluster_intersection_to_search_results(self, intersection_file_info_list: list, default_score: str = "S:1.0000[p:0,i:1.0000,t:(raw:1.0000)]") -> list:
        """
        将cluster交集结果转换为search结果格式
        这个方法专门用于处理intersect_face_lists的返回结果
        
        Args:
            intersection_file_info_list: intersect_face_lists返回的file_info列表
            default_score: 默认评分字符串
            
        Returns:
            list: search结果格式的列表
        """
        return self.convert_file_info_list_to_search_results(intersection_file_info_list, default_score)


# 创建全局转换器实例
converter = FileInfoConverter()


def convert_file_info_to_search_result(file_info: dict, default_score: str = "S:1.0000[p:0,i:1.0000,t:(raw:1.0000)]") -> list:
    """便捷函数：转换单个file_info"""
    return converter.convert_file_info_to_search_result(file_info, default_score)


def convert_file_info_list_to_search_results(file_info_list: list, default_score: str = "S:1.0000[p:0,i:1.0000,t:(raw:1.0000)]") -> list:
    """便捷函数：转换file_info列表"""
    return converter.convert_file_info_list_to_search_results(file_info_list, default_score)


def convert_face_list_to_search_results(face_list: list, default_score: str = "S:1.0000[p:0,i:1.0000,t:(raw:1.0000)]") -> list:
    """便捷函数：转换face_list"""
    return converter.convert_face_list_to_search_results(face_list, default_score)


def convert_cluster_intersection_to_search_results(intersection_file_info_list: list, default_score: str = "S:1.0000[p:0,i:1.0000,t:(raw:1.0000)]") -> list:
    """便捷函数：转换cluster交集结果"""
    return converter.convert_cluster_intersection_to_search_results(intersection_file_info_list, default_score)


if __name__ == '__main__':
    # 测试代码
    test_file_info = {
        "file_key": "NSQAZWNjYmFkZDctM2JiYy00NmVmLWI3NjktYjNkNGUwODY1ZGViBwBwaG90b2Nx",
        "tags": {
            "Albumid": "7e83985a-22b0-4cf0-8133-4eddd59ec646",
            "lowClarityFace": "0",
            "originTag": "originTag,Albumid",
            "url": "https://photocq.photo.store.qq.com/psc?/7e83985a-22b0-4cf0-8133-4eddd59ec646/WPRBLIzw1M1MS7DNq9ysQOLsbDyXCHJhIoWt4VqJNvUxu0T9iJiJgVQy*Tr75KXVdEM3PNKNrrrZ7HaeGbzp5ddtgclhteoyLBr7gpcFKVk!/b"
        },
        "file_type": 0
    }
    
    result = convert_file_info_to_search_result(test_file_info)
    if result:
        print("转换成功:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print("转换失败")
