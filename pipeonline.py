import argparse
import numpy as np
import time

from scripts.util import tqdm_log
from scripts.embedding import <PERSON><PERSON>lip<PERSON>2<PERSON>mbe<PERSON>, BgeM3Embedder
from scripts.db import MongoDatabase

_text_embedder = None
_image_embedder = None
_database_map = {}
_test_uid = "test_uid"

def prepare_embedder(bge_path = 'checkpoints/bge-m3/2025-04-27-15-56/ep30-bs8-lr2e-5'):
    global _text_embedder
    global _image_embedder

    if _text_embedder is not None:
        tqdm_log(f"_text_embedder has already inited")
    else:
        _text_embedder = BgeM3Embedder(bge_path)
        _text_embedder.encode_text([""])

    if _image_embedder is not None:
        tqdm_log(f"_image_embedder has already inited")
    else:
        _image_embedder = WeClipV2Embedder()
        _image_embedder.load_model()

def get_database(uid = _test_uid):
    global _database_map
    if uid not in _database_map:
        _database_map[uid] = MongoDatabase(uid)
    return _database_map[uid]

def search(text, limit = None, uid = _test_uid, bge_path = None):
    time_start = time.time()
    prepare_embedder(bge_path)
    prepare_timecost = time.time() - time_start
    time_start = time.time()
    database = get_database(uid)
    load_db_timecost = time.time() - time_start
    global _text_embedder
    global _image_embedder

    time_start = time.time()
    text_query_emb = _text_embedder.encode_text(text) # [1024]
    text_emb_timecost = time.time() - time_start

    time_start = time.time()
    image_query_emb = _image_embedder.encode_text(text) # T2I, 所以也是encode_text, [1, 768]
    image_query_emb = image_query_emb[0].cpu() # [768]
    image_emb_timecost = time.time() - time_start

    # Calculate score distributions for this query
    tqdm_log(f"Score distribution for query '{text}':")

    # Calculate image score distribution against image embeddings
    image_dist = database.get_score_distribution(query_emb=image_query_emb)
    tqdm_log(f"Image scores (vs image embeddings): min:{image_dist['image_scores']['min']:.4f}, max:{image_dist['image_scores']['max']:.4f}, mean:{image_dist['image_scores']['mean']:.4f}")

    # Calculate text score distribution against text embeddings
    text_dist = database.get_score_distribution(query_emb=text_query_emb)
    tqdm_log(f"Text scores (vs text embeddings): min:{text_dist['text_scores']['min']:.4f}, max:{text_dist['text_scores']['max']:.4f}, mean:{text_dist['text_scores']['mean']:.4f}")

    time_start = time.time()
    results = database.query(text, text_query_emb, image_query_emb, top_n=limit)
    query_timecost = time.time() - time_start
    tqdm_log(f"search for [{text}], results len:{len(results)}, prepare_timecost:{prepare_timecost}, load_db_timecost:{load_db_timecost}, text_emb_timecost:{text_emb_timecost}, image_emb_timecost:{image_emb_timecost}, query_timecost:{query_timecost}")
    return results

def search_all(limit = None, uid = _test_uid):
    """获取所有图片结果，不进行文本搜索，按上传时间或其他默认顺序排序"""
    time_start = time.time()
    database = get_database(uid)
    load_db_timecost = time.time() - time_start

    time_start = time.time()
    results = database.query_all(top_n=limit)
    query_timecost = time.time() - time_start
    tqdm_log(f"search_all, results len:{len(results)}, load_db_timecost:{load_db_timecost}, query_timecost:{query_timecost}")
    return results

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--text', type=str, default=' ', help='search text')
    parser.add_argument('--limit', type=int, default=10, help='maximum number of items to search')
    parser.add_argument('--uid', type=str, default="test_uid", help='user id')
    parser.add_argument('--bge_path', type=str, default='checkpoints/bge-m3/2025-04-27-15-56/ep30-bs8-lr2e-5', help='从指定路径加载bge模型')

    args = parser.parse_args()

    tqdm_log(f"args:{args}")
    results = search(text=args.text, limit=args.limit, uid=args.uid, bge_path=args.bge_path)
    for result in results:
        score = result[1]
        photo = result[2]
        embedding = result[3]
        tqdm_log(f"search result:[score:{score}, embedding:{embedding}, url:{photo['url']}]")