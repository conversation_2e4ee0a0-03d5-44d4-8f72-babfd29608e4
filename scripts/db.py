import numpy as np
import time
import torch
from pymongo import MongoClient
from collections import OrderedDict
from scripts.util import tqdm_log
from heapq import nlargest

# 可配置的权重参数
IMAGE_EMB_WEIGHT = 0.7
TEXT_EMB_WEIGHT = 0.3
TEXT_MATCH_BOOST = 0.5  # Reduced from 1 to make the boost more subtle

# Score thresholds for filtering
IMAGE_SCORE_THRESHOLD = 0.735  # Minimum image embedding similarity score
TEXT_SCORE_THRESHOLD = 0.55    # Minimum text embedding similarity score

def _normalize(emb):
    emb = np.array(emb)
    return emb / (np.linalg.norm(emb, axis=0, keepdims=True) + 1e-8)

class ScoreData(object):
    def __init__(self, image_emb_score, raw_text_score, match_plain_text):
        self.image_emb_score = image_emb_score      # image embedding相似度得分
        self.match_plain_text = match_plain_text    # 是否明文匹配
        self.raw_text_score = raw_text_score        # text embedding原始相似度得分
    
    def score(self):
        # Calculate raw score without clamping
        embedding_score = self.image_emb_score * IMAGE_EMB_WEIGHT + self.raw_text_score * TEXT_EMB_WEIGHT 
        plain_text_score = TEXT_MATCH_BOOST if self.match_plain_text else 0
        return embedding_score + plain_text_score

    def desc(self):
        # Calculate raw text score before normalization
        return f"S:{self.score():.4f}[p:{1 if self.match_plain_text else 0},i:{self.image_emb_score:.4f},t:(raw:{self.raw_text_score:.4f})]"

class PhotoData(object):
    def __init__(self, db_info):
        self.db_info = db_info
        self.id = db_info['photo_id']
        self.norm_image_emb = np.array(db_info['image_embedding'])
        self.norm_text_emb = np.array(db_info.get('text_embedding', np.zeros(1024)))
        self._preprocess_text()
    
    def _preprocess_text(self):
        fields = [
            self.db_info.get('photo_title', '').lower(),
            self.db_info.get('photo_desc', '').lower()
        ] + [ocr['text'].lower() for ocr in self.db_info.get('ocr', [])]
        self.plain_text = '\x1e'.join({f.strip() for f in fields if f.strip()})
    
    def desc(self):
        photo = self.db_info
        # Use get() with default values for all fields
        shoot_time = photo.get('shoot_time', '')
        upload_time = photo.get('upload_time', '')
        a_title = photo.get('album_title', '')
        a_desc = photo.get('album_desc', '')
        p_title = photo.get('photo_title', '')
        p_desc = photo.get('photo_desc', '')
        ocr = [n['text'] for n in photo.get('ocr', [])]
        return f"[pid:{self.id}, s_t:{shoot_time}, u_t:{upload_time}, a_title:{a_title}, a_dsc:{a_desc}, p_title:{p_title}, p_dsc:{p_desc}, ocr:{ocr}]"


class MongoDatabase(object):
    def __init__(self, uid, drop=False, db_name=None, collection_name=None):
        """
        Initialize MongoDB connection with customizable database and collection names
        
        Args:
            uid: User ID, used as default collection name suffix if collection_name not provided
            drop: Whether to drop existing collection
            db_name: Custom database name (default: "qas")
            collection_name: Custom collection name (default: "feature_{uid}")
        """
        self.client = MongoClient("mongodb://localhost:27017/")
        self.db = self.client[db_name if db_name else "qas"]
        self.collection_name = collection_name if collection_name else f"feature_{uid}"
        self.collection = self.db[self.collection_name]
        if drop:
            self.collection.drop()
            tqdm_log(f"drop collection:{self.collection_name}")
        self.memcache_data = []
        cost = self.update_memcache()
        tqdm_log(f"load data db: {db_name} collection: {collection_name} to memcache, cost: {cost}, count: {len(self.memcache_data)}")

    def save(self, unique_key, feature):
        text_emb = feature.get('text_embedding')
        if text_emb is not None:
            feature['text_embedding'] = _normalize(text_emb).tolist()
        
        image_emb = feature.get('image_embedding')
        if image_emb is not None:
            feature['image_embedding'] = _normalize(image_emb).tolist()

        self.collection.update_one(
            {unique_key: feature[unique_key]},  # 查询条件(匹配唯一键)
            {"$set": feature},                  # 更新字段(完全覆盖或新增)
            upsert=True                         # 不存在则插入
        )

    def find_one(self, field_name, value):
        """Find one document by field name and value"""
        result = None
        try:
            result = self.collection.find_one({field_name: value})
        except Exception as e:
            tqdm_log(f"❌ Database query failed for {value} in field:{field_name}: {str(e)}")
        return result

    def update_memcache(self):
        start_time = time.time()
        mongo_count = self.collection.count_documents({})
        local_count = len(self.memcache_data)
        if local_count == mongo_count:
            return time.time() - start_time

        photo_list = []
        cursor = self.collection.find({}, batch_size=1000)
        for db_item in cursor:
            photo = PhotoData(db_item)
            photo_list.append(photo)
        self.memcache_data = photo_list
        return time.time() - start_time
    
    def calc_photo_score(self, photos, text_query, text_emb_norm, image_emb_norm):
        start_time = time.time()
        score_dict = {}
        
        # 计算图像相似度分数
        if image_emb_norm is not None:
            image_embs = np.stack([p.norm_image_emb for p in photos])  # [n, dim]
            image_scores = np.dot(image_embs, image_emb_norm.T)
            image_dist = self.get_score_distribution(query_emb=image_emb_norm)
        else:
            image_scores = np.zeros(len(photos))
        
        # 计算文本相似度分数
        if text_emb_norm is not None:
            text_embs = np.stack([p.norm_text_emb for p in photos])    # [n, dim]
            raw_text_scores = np.dot(text_embs, text_emb_norm.T)
        else:
            raw_text_scores = np.zeros(len(photos))
        
        # 归一化到[0,1]范围
        def normalize(scores, min_val, max_val):
            return (scores - min_val) / (max_val - min_val + 1e-8)
            
        # 图像分数归一化
        image_scores = normalize(image_scores, 
                               image_dist['image_scores']['min'], 
                               image_dist['image_scores']['max'])
        
        score_dict = {} # {pid: ScoreData}
        text_split = text_query.split() # 将搜索词按空白符拆分为多个关键词
        for i, photo in enumerate(photos):
            match_plain_text = any(text in photo.plain_text for text in text_split) # 任意关键词命中则认为命中明文匹配
            score_dict[photo.id] = ScoreData(image_scores[i], raw_text_scores[i],match_plain_text)

        return score_dict, time.time() - start_time

    def get_score_distribution(self, query_emb=None):
        """Calculate score distribution from all embeddings in database
        Args:
            query_emb: if provided, calculate scores against this embedding instead of self-similarity
        """
        if not self.memcache_data:
            self.update_memcache()
            
        # Get all embeddings
        image_embs = np.stack([p.norm_image_emb for p in self.memcache_data])
        text_embs = np.stack([p.norm_text_emb for p in self.memcache_data])
        
        if query_emb is None:
            # Self-similarity mode
            image_scores = np.dot(image_embs, image_embs.T)
            text_scores = np.dot(text_embs, text_embs.T)
            
            # Get upper triangle (excluding diagonal)
            image_triu = image_scores[np.triu_indices_from(image_scores, k=1)]
            text_triu = text_scores[np.triu_indices_from(text_scores, k=1)]
        else:
            # Query mode (like actual search)
            if isinstance(query_emb, torch.Tensor):
                # For PyTorch tensors, use .mT for matrix transpose
                query_emb_t = query_emb.mT if query_emb.dim() > 1 else query_emb.unsqueeze(1)
                query_emb_np = query_emb_t.cpu().numpy()
            else:
                # For numpy arrays, keep using .T
                query_emb_np = query_emb.T
            
            # Calculate scores based on query embedding dimension
            if query_emb_np.shape[0] == image_embs.shape[1]:  # Image query (768 dim) 
                image_scores = np.dot(image_embs, query_emb_np).flatten()
                image_triu = image_scores
                text_triu = np.array([])  # Skip text scores for image query
            elif query_emb_np.shape[0] == text_embs.shape[1]:  # Text query (1024 dim)
                text_scores = np.dot(text_embs, query_emb_np).flatten()
                text_triu = text_scores
                image_triu = np.array([])  # Skip image scores for text query
            else:
                raise ValueError(f"Query embedding dimension {query_emb_np.shape[0]} doesn't match image ({image_embs.shape[1]}) or text ({text_embs.shape[1]}) embeddings")
        
        result = {
            'image_scores': {
                'min': float(np.min(image_triu)) if len(image_triu) > 0 else 0.0,
                'max': float(np.max(image_triu)) if len(image_triu) > 0 else 0.0,
                'mean': float(np.mean(image_triu)) if len(image_triu) > 0 else 0.0,
                'std': float(np.std(image_triu)) if len(image_triu) > 0 else 0.0,
                'histogram': np.histogram(image_triu, bins=20, range=(-1, 1))[0].tolist() if len(image_triu) > 0 else [0]*20
            }
        }
        
        # Only include text scores if we have data
        if len(text_triu) > 0:
            result['text_scores'] = {
                'min': float(np.min(text_triu)),
                'max': float(np.max(text_triu)),
                'mean': float(np.mean(text_triu)),
                'std': float(np.std(text_triu)),
                'histogram': np.histogram(text_triu, bins=20, range=(-1, 1))[0].tolist()
            }
        else:
            result['text_scores'] = {
                'min': 0.0,
                'max': 0.0,
                'mean': 0.0,
                'std': 0.0,
                'histogram': [0]*20
            }
            
        return result

    def query(self, text, text_emb, image_emb, top_n=None, enable_log=True):
        # Handle None embeddings
        text_emb_norm = _normalize(text_emb) if text_emb is not None else None
        image_emb_norm = _normalize(image_emb) if image_emb is not None else None
        
        db_cost = self.update_memcache()
        if not self.memcache_data:
            tqdm_log("Warning: No data in database, returning empty results")
            return []

        all_photos = self.memcache_data
        score_dict, calc_score_cost = self.calc_photo_score(all_photos, text, text_emb_norm, image_emb_norm)

        start_time = time.time()
        # Filter photos based on score thresholds
        # First filter all photos that meet the criteria
        filtered_photos = [
            p for p in all_photos
            if (score_dict[p.id].match_plain_text or 
                score_dict[p.id].image_emb_score >= IMAGE_SCORE_THRESHOLD or 
                score_dict[p.id].raw_text_score >= TEXT_SCORE_THRESHOLD)
        ]

        # Sort all filtered photos by total score in descending order
        filtered_photos.sort(
            key=lambda p: score_dict[p.id].score(), 
            reverse=True
        )
        
        # Then take top 50 results after sorting
        filtered_photos = filtered_photos[:50]

        results = []
        for i, p in enumerate(filtered_photos):
            if enable_log:
                tqdm_log(f"[{i}] {score_dict[p.id].desc()} -> {p.desc()}")

            score_desc_str = score_dict[p.id].desc(),
            result = [p.id, score_desc_str, p.db_info]
            results.append(result)
        return results

    def query_all(self, top_n=None, enable_log=True):
        """获取所有图片结果，不进行文本搜索，按上传时间排序"""
        db_cost = self.update_memcache()
        if not self.memcache_data:
            tqdm_log("Warning: No data in database, returning empty results")
            return []

        all_photos = self.memcache_data

        # 按上传时间排序（如果有的话），否则按photo_id排序
        try:
            all_photos.sort(key=lambda p: p.db_info.get('upload_time', '0'), reverse=True)
        except:
            # 如果排序失败，按photo_id排序
            all_photos.sort(key=lambda p: p.id, reverse=True)

        # 限制返回数量
        if top_n:
            all_photos = all_photos[:top_n]

        results = []
        for i, p in enumerate(all_photos):
            if enable_log and i < 10:  # 只打印前10个结果的日志
                tqdm_log(f"[{i}] query_all -> {p.desc()}")

            # 为了保持格式一致，创建一个默认的评分描述
            score_desc_str = "S:1.0000[p:0,i:1.0000,t:(raw:1.0000)]",
            result = [p.id, score_desc_str, p.db_info]
            results.append(result)

        tqdm_log(f"query_all returned {len(results)} results")
        return results