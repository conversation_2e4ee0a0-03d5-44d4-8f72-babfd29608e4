import os
import time
import torch

from enum import Enum
from typing import Union, List
from weclipv2.weclipv2 import WeCLIPv2Large, WeCLIPv2Base, WeCLIPv2Small
from FlagEmbedding import BGEM3FlagModel
from scripts.util import tqdm_log

class WeClipV2Type(Enum):
    LARGE = (WeCLIPv2Large, "checkpoints/weclipv2/weclipv2_large16_256.pth")
    BASE = (WeCLIPv2Base, "checkpoints/weclipv2/weclipv2_base16_256.pth")
    SMALL = (WeCLIPv2Small, "checkpoints/weclipv2/weclipv2_small_256.pth")

class WeClipV2Embedder(object):
    def __init__(self, type: WeClipV2Type = WeClipV2Type.LARGE):
        self.type = type
        self.model_class, self.model_path = type.value
        self.model = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    def load_model(self):
        if self.model is not None:
            tqdm_log(f'model {self.model_path} has already inited')
            return
        
        tqdm_log(f'WeClipV2Embedder: load model start:{self.model_path}')
        time1 = time.time()
        self.model = self.model_class().to(self.device)
        state_dict = torch.load(self.model_path, map_location=self.device)
        self.model.load_state_dict(state_dict)
        self.model.eval()
        self.tokenizer = self.model.get_tokenizer()
        time2 = time.time()
        tqdm_log(f'WeClipV2Embedder: load model {self.model_path} finish, cost:{time2 - time1}')


    def encode_text(self, text: Union[List[str], str]):
        if self.model is None:
            self.load_model()

        with torch.no_grad():
            time3 = time.time()
            # tokenizer = self.model.get_tokenizer()
            # text_token_id, text_mask = self.model.preprocess_text(text, tokenizer)
            ret = self.tokenizer(
                    text,
                    # padding='max_length',
                    truncation=True,
                    max_length=72,
                    return_tensors="pt"
                )
            text_token_id = ret['input_ids']
            text_mask = ret['attention_mask']
            
            text_token_id = text_token_id.to(self.device)
            text_mask = text_mask.to(self.device)
            text_feature = self.model.forward_text(text_token_id, text_mask)
            time4 = time.time()
            # tqdm_log(f'encode_text {text} cost {time4 - time3}')
            return text_feature

    def encode_image(self, image_path):
        if self.model is None:
            self.load_model()
        
        with torch.no_grad():
            time_start = time.time()
            if self.type == WeClipV2Type.SMALL:
                img = self.model.preprocess_image(image_path)
                img = img.unsqueeze(0).to(self.device)
            else:
                patch_img, patch_pos = self.model.preprocess_image(image_path)
                patch_img = patch_img.unsqueeze(0).to(self.device)
                patch_pos = patch_pos.unsqueeze(0).to(self.device)
            preprocess_cost = time.time() - time_start
            time_start = time.time()

            if self.type == WeClipV2Type.SMALL:
                image_feature = self.model.forward_visual(img)
            else:
                image_feature = self.model.forward_visual(patch_img, patch_pos)
            forward_cost = time.time() - time_start
            # tqdm_log(f'encode_image preprocess_cost:{preprocess_cost:.4f}, forward_cost:{forward_cost:.4f}')
            return image_feature

class BgeM3Embedder(object):
    def __init__(self, model_path = None):
        self.model_path = model_path if model_path and os.path.exists(model_path) else 'BAAI/bge-m3'
        tqdm_log(f"bge-m3 model_path:{self.model_path}")
        self.model = BGEM3FlagModel(self.model_path, use_fp16=True)

    def encode_text(self, text: Union[List[str], str]):
        embedding = self.model.encode(text, batch_size=12, max_length=8192)
        return embedding['dense_vecs']