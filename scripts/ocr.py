from paddleocr import PaddleOC<PERSON>
from PIL import Image
import numpy as np
from io import BytesIO
from typing import Union

class OCRProcessor:
    def __init__(self, det_model_dir="checkpoints/ocr_model_path/det_model_dir", cls_model_dir="checkpoints/ocr_model_path/cls_model_dir", rec_model_dir="checkpoints/ocr_model_path/rec_model_dir"):
        self.ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False, 
                             det_model_dir=det_model_dir, cls_model_dir=cls_model_dir, rec_model_dir=rec_model_dir)

    def preprocess_image(self, img_input: Union[str, bytes]) -> np.ndarray:
        if isinstance(img_input, str):
            image = Image.open(img_input)
        elif isinstance(img_input, bytes):
            image = Image.open(BytesIO(img_input))
        else:
            raise TypeError(f"Unknown type of input image. {type(img_input)}")

        return np.array(image)

    def calculate_rotation_angle(self, boxes):
        left_top, right_top, right_bottom, left_bottom = boxes
        right_mid_y = (right_top[1] + right_bottom[1]) / 2
        left_mid_y = (left_top[1] + left_bottom[1]) / 2
        horizontal_distance = right_top[0] - left_top[0]
        vertical_distance = right_mid_y - left_mid_y
        angle_radians = np.arctan2(vertical_distance, horizontal_distance)
        angle_degrees = np.degrees(angle_radians)

        return angle_degrees

    def ocr_image(self, img_input: Union[str, bytes]):
        image = self.preprocess_image(img_input)
        result = self.ocr.ocr(image, cls=True)[0]
        if result is None:
            return []

        ocr_results = []

        for line in result:
            boxes = line[0]
            txts = line[1][0]
            scores = line[1][1]
            angle = self.calculate_rotation_angle(boxes)

            ocr_results.append({
                'boxes': boxes,
                'text': txts,
                'score': round(scores, 4),
                'angle': round(angle, 2) 
            })

        return ocr_results
