import sys
import requests
import logging
import subprocess
import os
import json
import cv2
import numpy as np

from datetime import datetime
from typing import List, Dict, Tuple, Optional
from tempfile import NamedTemporaryFile
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        # logging.FileHandler('qzone_search.log'),  # 输出到文件
        logging.StreamHandler(sys.stdout)  # 输出到控制台
    ]
)
logger = logging.getLogger(__name__)

def log(message, level='info'):
    """
    日志记录函数
    
    参数:
        message (str): 日志信息
        level (str): 日志级别，可选值：'debug', 'info', 'warning', 'error', 'critical'
    """
    level = level.lower()
    if level == 'debug':
        logger.debug(message)
    elif level == 'info':
        logger.info(message)
    elif level == 'warning':
        logger.warning(message)
    elif level == 'error':
        logger.error(message)
    elif level == 'critical':
        logger.critical(message)
    else:
        logger.info(message)  # 默认使用info级别

def tqdm_log(msg):
    tqdm.write(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {msg}")

def resize_image_bytes(bytes, size: Tuple[int, int]):
    nparr = np.fromstring(bytes, np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    if image is None:
        return bytes

    original_height, original_width = image.shape[:2]
    image = cv2.resize(image, size, interpolation=cv2.INTER_LINEAR)
    success, encode_image = cv2.imencode('.png', image)
    if success:
        print(f"resize from [{original_width}, {original_height}] to {size}")
        return encode_image.tobytes()
    else:
        return bytes



def url_to_bytes(url: str, size: Optional[Tuple[int, int]] = None) -> bytes:
    """从 URL 下载图片并返回 bytes"""
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        bytes = response.content
        if size is not None:
            return resize_image_bytes(bytes, size)
        else:
            return bytes
    except requests.exceptions.RequestException as e:
        log(f"下载图片失败: {e}, url:{url}")
        raise
    except Exception as e:
        log(f"未知错误: {e}, url:{url}")
        raise

def time_readable(timestamp):
    return datetime.utcfromtimestamp(int(timestamp)).strftime('%Y-%m-%d')

def parse_photo_data(file_path):
    all_photos = []
    
    with open(file_path, 'r', encoding='utf-8') as file:
        for idx, content in enumerate(file, 1):
            stripped = content.strip() # 去除前后空白字符
            if not stripped:
                continue
                
            try:
                photo = json.loads(stripped)
                all_photos.append(photo)
                
            except json.JSONDecodeError as e:
                log(f"Line {idx}: JSON decode error: {e}, content:{content}")
            except TypeError as e:
                log(f"Line {idx}: Type error: {e}, content:{content}")
            except Exception as e:
                log(f"Line {idx}: Unexpected error: {e}, content:{content}")
                
    return all_photos

def rsync_download(uid: str, psw: str) -> List[Dict]:
    """
    使用rsync下载指定uid的图片资源并解析其内容
    """
    with NamedTemporaryFile(prefix=f"{uid}_", suffix=".jsonl", mode='w+', delete=False) as tmp_file:
        tmp_path = tmp_file.name
    
    try:
        remote_url = f"rsync://**********:/rsyncdata/{uid}.json"
        with NamedTemporaryFile(mode='w', delete=True) as pw_file:
            pw_file.write(psw)
            pw_file.flush()
            os.chmod(pw_file.name, 0o600)

            cmd = [
                'rsync', '-av',
                '--password-file', pw_file.name,
                remote_url,
                tmp_path
            ]

            result = subprocess.run(
                cmd,
                check=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=30
            )
            
        return parse_photo_data(tmp_path)
        
    except json.JSONDecodeError as e:
        raise RuntimeError(f"数据解析失败: {str(e)}") from e
    except subprocess.CalledProcessError as e:
        error_msg = e.stderr.decode().replace(psw, '***').strip()
        raise RuntimeError(f"下载失败: {error_msg}") from e
    except subprocess.TimeoutExpired:
        raise RuntimeError("连接超时，请检查网络连接")
    finally:
        # 清理临时文件
        if os.path.exists(tmp_path):
            os.remove(tmp_path)

def get_rsync_password():
    if not hasattr(get_rsync_password, 'password'):
        password = input("🔑 请输入rsync密码: ")
        setattr(get_rsync_password, 'password', password)
    return get_rsync_password.password

def file_to_json(path: str):
    try:
        # 读取并解析JSON文件
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)            
    except FileNotFoundError:
        print(f"文件不存在：{path}")
        return None
    except json.JSONDecodeError:
        print(f"文件格式错误：{path}")
        return None
    except Exception as e:
        print(f"读取 {path} 失败：{str(e)}")
        return None

def file_to_jsonl(path: str):
    data = []
    try:
        with open(path, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                stripped_line = line.strip()
                if not stripped_line:
                    continue
                try:
                    item = json.loads(stripped_line)
                    data.append(item)
                except json.JSONDecodeError as e:
                    print(f"文件格式错误：{path}，第 {line_num} 行 - {e}，内容：{stripped_line}")
    except FileNotFoundError:
        print(f"文件不存在：{path}")
        return None
    except Exception as e:
        print(f"读取 {path} 失败：{str(e)}")
        return None
    return data